import { NextRequest } from 'next/server';
import { auth } from "@/auth";
import { isUserWithinCreditQuota } from '../actions';
import proxyOpenAiStream from './proxyOpenAiStream';

// Vercel Hobby 默认 10s，最大 60
export const maxDuration = 60;

export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ workspace: string }> }
) {
  const session = await auth();
  if (!session) {
    return new Response(JSON.stringify({ error: 'Unauthorized' }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' },
    });
  }
  const userId = session.user.id;

  // 从动态路由参数中获取 workspaceId
  const { workspace: workspaceId } = await params;

  try {
    const userRequestHeaders = req.headers;
    const xProvider = 'hivechat';
    const xModel = decodeURIComponent(userRequestHeaders.get('X-Model') || '');
    const xChatId = userRequestHeaders.get('x-chat-id');
    const xEndpoint = process.env.HIVECHAT_ENDPOINT + '/chat/completions';

    const isUserWithinCreditQuotaResult = await isUserWithinCreditQuota(userId, xProvider);
    if (!isUserWithinCreditQuotaResult) {
      return new Response(JSON.stringify({ error: 'Out of quota' }), {
        status: 459,
        headers: { 'Content-Type': 'application/json' },
      });
    }
    const apikey = process.env.HIVECHAT_APIKEY || ''; 
    const headers = new Headers({
      'Content-Type': 'application/json',
      'Connection': 'keep-alive',
      'Authorization': `Bearer ${apikey}`,
    });
    // 获取请求体
    const body = await req.text();
    const response = await fetch(xEndpoint, {
      method: 'POST',
      headers: headers,
      body: body,
    });
    // 检查响应是否成功
    if (!response.ok) {
      const errorData = await response.text();
      return new Response(errorData, {
        status: response.status,
        headers: { 'Content-Type': 'application/json' },
      });
    }
    return proxyOpenAiStream(response, {
      chatId: xChatId || undefined,
      workspaceId: workspaceId,
      model: xModel,
      userId: userId,
      providerId: xProvider
    });

  } catch (error) {
    return new Response(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

