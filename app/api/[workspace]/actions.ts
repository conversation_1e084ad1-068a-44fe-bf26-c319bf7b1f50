'use server';
import { db } from '@/app/db';
import { eq, and, sql } from 'drizzle-orm'
import { chats, usageReport, userWorkspace, groups, creditTransactions } from '@/app/db/schema';
import { CreditCalculation, calculateCredits } from './hivechat/price';

type UsageType = {
  inputTokens: number,
  outputTokens: number,
  totalTokens: number,
}

type UsageDetail = {
  chatId?: string,
  messageId?: number,
  date: string,
  userId: string,
  modelId: string,
  providerId: string,
  inputTokens: number,
  outputTokens: number,
  totalTokens: number,
  workspaceId: string,
}

export const isUserWithinCreditQuota = async (userId: string, workspaceId: string): Promise<boolean> => {
  try {
    // 查询用户在指定工作空间的信息，获取积分余额
    const userWorkspaceResult = await db.query.userWorkspace.findFirst({
      where: and(
        eq(userWorkspace.userId, userId),
        eq(userWorkspace.workspaceId, workspaceId),
        eq(userWorkspace.isActive, true)
      ),
      columns: {
        creditBalance: true,
      }
    });

    // 如果找不到对应的 userWorkspace 记录，返回 false
    if (!userWorkspaceResult) {
      return false;
    }

    // 检查积分余额是否大于 0
    return userWorkspaceResult.creditBalance > 0;
  } catch (error) {
    // 处理数据库查询异常，记录错误并返回 false
    console.error('Error checking user credit quota:', error);
    return false;
  }
}

export const isUserWithinQuota = async (userId: string, providerId: string, modelId: string, workspaceId: string):
  Promise<{ tokenPassFlag: boolean, modelPassFlag: boolean }> => {

  // 查询用户在指定工作空间的信息，包括用量数据、组信息和角色信息
  const userWorkspaceResult = await db.query.userWorkspace.findFirst({
    where: and(
      eq(userWorkspace.userId, userId),
      eq(userWorkspace.workspaceId, workspaceId),
      eq(userWorkspace.isActive, true)
    ),
    columns: {
      usageUpdatedAt: true,
      currentMonthTotalTokens: true,
      groupId: true,
      role: true,
    }
  });

  let tokenPassFlag = false;
  let modelPassFlag = false;

  // 如果用户不在工作空间中，拒绝访问
  if (!userWorkspaceResult) {
    return {
      tokenPassFlag: false,
      modelPassFlag: false,
    };
  }

  // 检查用户是否为工作空间管理员（admin 或 owner）
  const isWorkspaceAdmin = userWorkspaceResult.role === 'admin' || userWorkspaceResult.role === 'owner';

  // 如果是管理员，modelPassFlag 始终为 true，但 tokenPassFlag 仍需遵守配额限制
  if (isWorkspaceAdmin) {
    modelPassFlag = true;
  }

  // 如果用户有关联的组，获取组信息
  let groupResult = null;
  if (userWorkspaceResult.groupId) {
    groupResult = await db.query.groups.findFirst({
      where: and(
        eq(groups.id, userWorkspaceResult.groupId),
        eq(groups.workspaceId, workspaceId)
      ),
      columns: {
        tokenLimitType: true,
        monthlyTokenLimit: true,
        modelType: true,
      },
      with: {
        models: {
          with: {
            model: {
              columns: {
                name: true,
                providerId: true,
              }
            }
          }
        }
      }
    }) as {
      tokenLimitType: 'unlimited' | 'limited';
      monthlyTokenLimit: number | null;
      modelType: 'all' | 'specific';
      models?: Array<{
        model: {
          name: string;
          providerId: string;
        }
      }>;
    } | null;
  }

  // 如果用户有关联的组
  if (groupResult) {
    const { tokenLimitType, monthlyTokenLimit, modelType } = groupResult;
    const monthlyTokenLimitNumber = monthlyTokenLimit || 0;

    // 检查 token 配额
    if (tokenLimitType === 'unlimited') {
      tokenPassFlag = true;
    } else {
      const now = new Date();
      const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0);

      let realMonthlyTotalTokens = 0;
      if (userWorkspaceResult.usageUpdatedAt > firstDayOfMonth) {
        realMonthlyTotalTokens = userWorkspaceResult.currentMonthTotalTokens;
      }

      tokenPassFlag = realMonthlyTotalTokens < monthlyTokenLimitNumber;
    }

    // 检查模型权限（如果不是管理员才需要检查组权限）
    if (!isWorkspaceAdmin) {
      if (modelType === 'all') {
        modelPassFlag = true;
      } else {
        const hasMatchingModel = groupResult.models?.some(
          (groupModel: any) =>
            groupModel.model.providerId === providerId &&
            groupModel.model.name === modelId
        );
        modelPassFlag = hasMatchingModel || false;
      }
    }
  } else {
    // 如果用户没有分配到组，默认允许访问（向后兼容）
    tokenPassFlag = true;
    // 如果不是管理员且没有分配组，默认允许模型访问（向后兼容）
    if (!isWorkspaceAdmin) {
      modelPassFlag = true;
    }
  }
  return {
    tokenPassFlag,
    modelPassFlag,
  }
}

export const updateTokenAndCreditUsage = async (userId: string, usage: UsageDetail) => {
  const creditCalculationResult: CreditCalculation = calculateCredits(usage.modelId, usage.inputTokens, usage.outputTokens);

  // 用户在 workspace 下当月、当日的 token 用量
  await updateUserUsage(userId, usage.workspaceId, {
    inputTokens: usage.inputTokens,
    outputTokens: usage.outputTokens,
    totalTokens: usage.totalTokens,
  }, creditCalculationResult.totalCredits);

  // 此处实现更新 creditTransactions 表
  try {
    // 获取更新后的用户积分余额
    const updatedUserWorkspace = await db.query.userWorkspace.findFirst({
      where: and(
        eq(userWorkspace.userId, userId),
        eq(userWorkspace.workspaceId, usage.workspaceId),
        eq(userWorkspace.isActive, true)
      )
    });

    if (!updatedUserWorkspace) {
      throw new Error('User workspace not found after credit update');
    }

    // 插入积分消费流水记录
    await db.insert(creditTransactions).values({
      workspaceId: usage.workspaceId,
      userId: userId,
      transactionType: 'consumption',
      inputCredits: -creditCalculationResult.inputCredits,
      outputCredits: -creditCalculationResult.outputCredits,
      totalCredits: -creditCalculationResult.totalCredits, // 负数表示消费
      balanceAfter: updatedUserWorkspace.creditBalance,
      relatedEntityId: usage.messageId?.toString() || usage.modelId, // 如果有chatId使用chatId，否则使用modelId
      relatedEntityType: usage.chatId ? 'Message' : 'ModelUsage',
      notes: `Model: ${usage.modelId}, Input: ${usage.inputTokens} tokens, Output: ${usage.outputTokens} tokens`
    });
  } catch (error) {
    console.error('Failed to insert credit transaction:', error);
  }

  if (usage.chatId) {
    // 更新 Chat的用量（可选）
    updateChatUsage(usage.chatId, {
      inputTokens: usage.inputTokens,
      outputTokens: usage.outputTokens,
      totalTokens: usage.totalTokens,
    });
  }
  // 更新对于模型、用户、workspace 下的用量
  updateUsageReport(usage);
}

export const updateUsage = async (userId: string, usage: UsageDetail) => {
  updateUserUsage(userId, usage.workspaceId, {
    inputTokens: usage.inputTokens,
    outputTokens: usage.outputTokens,
    totalTokens: usage.totalTokens,
  });

  if (usage.chatId) {
    updateChatUsage(usage.chatId, {
      inputTokens: usage.inputTokens,
      outputTokens: usage.outputTokens,
      totalTokens: usage.totalTokens,
    });
  }
  updateUsageReport(usage);
}

export const updateUserUsage = async (userId: string, workspaceId: string, usage: UsageType, creditCost?: number) => {
  if (creditCost === undefined) {
    creditCost = 0
  }
  const userWorkspaceDetail = await db.query.userWorkspace
    .findFirst({
      where: and(
        eq(userWorkspace.userId, userId),
        eq(userWorkspace.workspaceId, workspaceId),
        eq(userWorkspace.isActive, true)
      )
    });

  if (!userWorkspaceDetail) {
    throw new Error('User not found in workspace');
  }

  // 获取今天0点的时间
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const now = new Date();
  const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0);

  if (userWorkspaceDetail.usageUpdatedAt && new Date(userWorkspaceDetail.usageUpdatedAt) < firstDayOfMonth) {
    // 如果最后更新时间早于本月 1 日 0点，重置计数
    await db.update(userWorkspace).set({
      todayTotalTokens: usage.totalTokens,
      currentMonthTotalTokens: usage.totalTokens,
      usageUpdatedAt: new Date(),
      creditBalance: sql`${userWorkspace.creditBalance} - ${creditCost}`,
    })
      .where(and(
        eq(userWorkspace.userId, userId),
        eq(userWorkspace.workspaceId, workspaceId)
      ));
  } else if (userWorkspaceDetail.usageUpdatedAt && new Date(userWorkspaceDetail.usageUpdatedAt) < today) {
    // 如果最后更新时间早于今日 0点，重置当日计数
    await db.update(userWorkspace).set({
      todayTotalTokens: usage.totalTokens,
      currentMonthTotalTokens: sql`${userWorkspace.currentMonthTotalTokens} + ${usage.totalTokens}`,
      usageUpdatedAt: new Date(),
      creditBalance: sql`${userWorkspace.creditBalance} - ${creditCost}`,
    })
      .where(and(
        eq(userWorkspace.userId, userId),
        eq(userWorkspace.workspaceId, workspaceId)
      ));
  } else {
    // 如果是本日内的更新，累加计数
    await db.update(userWorkspace).set({
      todayTotalTokens: sql`${userWorkspace.todayTotalTokens} + ${usage.totalTokens}`,
      currentMonthTotalTokens: sql`${userWorkspace.currentMonthTotalTokens} + ${usage.totalTokens}`,
      usageUpdatedAt: new Date(),
      creditBalance: sql`${userWorkspace.creditBalance} - ${creditCost}`,
    })
      .where(and(
        eq(userWorkspace.userId, userId),
        eq(userWorkspace.workspaceId, workspaceId)
      ));
  }
}

const updateChatUsage = async (chatId: string, usage: UsageType) => {
  await db.update(chats).set({
    inputTokens: sql`${chats.inputTokens} + ${usage.inputTokens}`,
    outputTokens: sql`${chats.outputTokens} + ${usage.outputTokens}`,
    totalTokens: sql`${chats.totalTokens} + ${usage.totalTokens}`,
  })
    .where(eq(chats.id, chatId))
}

const updateUsageReport = async (updateRecord: UsageDetail) => {
  const existingRecord = await db.select()
    .from(usageReport)
    .where(
      and(
        eq(usageReport.date, updateRecord.date),
        eq(usageReport.userId, updateRecord.userId),
        eq(usageReport.modelId, updateRecord.modelId),
        eq(usageReport.providerId, updateRecord.providerId),
        eq(usageReport.workspaceId, updateRecord.workspaceId)
      )
    )
    .limit(1);

  if (existingRecord.length > 0) {
    // 如果记录存在，更新tokens值
    await db.update(usageReport)
      .set({
        inputTokens: sql`${usageReport.inputTokens} + ${updateRecord.inputTokens}`,
        outputTokens: sql`${usageReport.outputTokens} + ${updateRecord.outputTokens}`,
        totalTokens: sql`${usageReport.totalTokens} + ${updateRecord.totalTokens}`,
      })
      .where(
        and(
          eq(usageReport.date, updateRecord.date),
          eq(usageReport.userId, updateRecord.userId),
          eq(usageReport.modelId, updateRecord.modelId),
          eq(usageReport.providerId, updateRecord.providerId),
          eq(usageReport.workspaceId, updateRecord.workspaceId)
        )
      );
  } else {
    // 如果记录不存在，插入新记录
    await db.insert(usageReport).values(updateRecord);
  }
}