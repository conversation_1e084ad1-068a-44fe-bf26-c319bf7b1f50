'use client';

import React, { useState, useEffect } from 'react';
import { Table, DatePicker, Input, Button, Space, message, Card, ConfigProvider } from 'antd';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import { useParams } from 'next/navigation';
import { getCreditTransactions, CreditTransactionRecord, CreditTransactionFilter } from './actions';
import dayjs from 'dayjs';
import locale from 'antd/locale/zh_CN';
import CreditConsumptionChart from './CreditConsumptionChart';

import 'dayjs/locale/zh-cn';

dayjs.locale('zh-cn');

const { RangePicker } = DatePicker;

const UsagePage = () => {
  const params = useParams();
  const workspaceId = params.workspace as string;

  // 状态管理
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<CreditTransactionRecord[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // 筛选条件
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs | null, dayjs.Dayjs | null] | null>(null);
  const [userEmail, setUserEmail] = useState('');

  // 加载数据
  const loadData = async (filters: CreditTransactionFilter = {}) => {
    setLoading(true);
    try {
      const result = await getCreditTransactions(workspaceId, {
        page: currentPage,
        pageSize,
        ...filters,
      });

      if (result.status === 'success' && result.data) {
        setData(result.data.data);
        setTotal(result.data.total);
      } else {
        message.error(result.message || '获取数据失败');
        setData([]);
        setTotal(0);
      }
    } catch (error) {
      message.error('获取数据时发生错误');
      setData([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    loadData();
  }, [workspaceId, currentPage, pageSize]);

  // 处理筛选
  const handleFilter = () => {
    const filters: CreditTransactionFilter = {
      page: 1, // 重置到第一页
      pageSize,
    };

    if (dateRange && dateRange[0] && dateRange[1]) {
      filters.startDate = dateRange[0].format('YYYY-MM-DD');
      filters.endDate = dateRange[1].format('YYYY-MM-DD');
    }

    if (userEmail.trim()) {
      filters.userEmail = userEmail.trim();
    }

    setCurrentPage(1);
    loadData(filters);
  };

  // 清空筛选条件
  const handleClearFilter = () => {
    setDateRange(null);
    setUserEmail('');
    setCurrentPage(1);
    loadData({ page: 1, pageSize });
  };

  // 刷新数据
  const handleRefresh = () => {
    const filters: CreditTransactionFilter = {
      page: currentPage,
      pageSize,
    };

    if (dateRange && dateRange[0] && dateRange[1]) {
      filters.startDate = dateRange[0].format('YYYY-MM-DD');
      filters.endDate = dateRange[1].format('YYYY-MM-DD');
    }

    if (userEmail.trim()) {
      filters.userEmail = userEmail.trim();
    }

    loadData(filters);
  };

  // 表格列定义
  const columns = [
    {
      title: '时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      render: (date: Date) => dayjs(date).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '用户邮箱',
      dataIndex: 'userEmail',
      key: 'userEmail',
      width: 200,
    },
    {
      title: '模型ID',
      dataIndex: 'modelId',
      key: 'modelId',
      width: 150,
    },
    {
      title: 'Input Token',
      dataIndex: 'inputCredits',
      key: 'inputCredits',
      width: 120,
      align: 'right' as const,
      render: (value: number) => value?.toLocaleString() || 0,
    },
    {
      title: 'Output Token',
      dataIndex: 'outputCredits',
      key: 'outputCredits',
      width: 120,
      align: 'right' as const,
      render: (value: number) => value?.toLocaleString() || 0,
    },
    {
      title: '消耗积分',
      dataIndex: 'totalCredits',
      key: 'totalCredits',
      width: 120,
      align: 'right' as const,
      render: (value: number) => value?.toLocaleString() || 0,
    },
  ];

  return (
    <div className="container mx-auto p-6">
      {/* 积分消耗统计图表 */}
      <CreditConsumptionChart workspaceId={workspaceId} />

      <Card>
        <div className="mb-6">
          <h1 className="text-xl font-bold mb-4">积分消耗明细</h1>

          {/* 筛选区域 */}
          <div className="mb-4 p-4 bg-gray-50 rounded-lg">
            <Space wrap size="middle">
              <div>
                <span className="text-sm text-gray-600 mr-2">日期范围:</span>
                <ConfigProvider locale={locale}>
                  <RangePicker
                    value={dateRange}
                    onChange={setDateRange}
                    format="YYYY-MM-DD"
                    placeholder={['开始日期', '结束日期']}
                  />
                </ConfigProvider>

              </div>

              <div>
                <span className="text-sm text-gray-600 mr-2">用户邮箱:</span>
                <Input
                  placeholder="输入用户邮箱"
                  value={userEmail}
                  onChange={(e) => setUserEmail(e.target.value)}
                  style={{ width: 200 }}
                  onPressEnter={handleFilter}
                />
              </div>

              <Space>
                <Button
                  type="primary"
                  icon={<SearchOutlined />}
                  onClick={handleFilter}
                >
                  筛选
                </Button>
                <Button onClick={handleClearFilter}>
                  清空
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                >
                  刷新
                </Button>
              </Space>
            </Space>
          </div>
        </div>

        {/* 表格 */}
        <Table
          columns={columns}
          dataSource={data}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            pageSizeOptions: ['10', '20', '50', '100'],
            onChange: (page, size) => {
              setCurrentPage(page);
              if (size !== pageSize) {
                setPageSize(size);
                setCurrentPage(1);
              }
            },
          }}
          scroll={{ x: 1000 }}
        />
      </Card>
    </div>
  );
};

export default UsagePage;