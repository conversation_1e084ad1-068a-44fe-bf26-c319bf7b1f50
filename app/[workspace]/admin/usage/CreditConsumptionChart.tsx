'use client';

import React, { useState, useEffect } from 'react';
import { Column } from '@ant-design/charts';
import { Card, Spin, Alert } from 'antd';
import { getCreditConsumptionStats, DailyCreditConsumption } from './actions';

interface CreditConsumptionChartProps {
  workspaceId: string;
}

const CreditConsumptionChart: React.FC<CreditConsumptionChartProps> = ({ workspaceId }) => {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<DailyCreditConsumption[]>([]);
  const [error, setError] = useState<string | null>(null);

  // 加载数据
  const loadData = async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await getCreditConsumptionStats(workspaceId);
      if (result.status === 'success' && result.data) {
        setData(result.data.data);
      } else {
        setError(result.message || '获取数据失败');
      }
    } catch (err) {
      setError('获取数据时发生错误');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (workspaceId) {
      loadData();
    }
  }, [workspaceId]);

  // 图表配置
  const config = {
    data,
    style: {
      columnWidthRatio: 0.6, // 0 ~ 1
    },
    xField: 'date',
    yField: 'totalCredits',
    columnStyle: {
      radius: [4, 4, 0, 0],
    },
    color: '#1890ff',
    tooltip: {
      title: '日期',
      formatter: (datum: any) => {
        return {
          name: '积分消耗',
          value: `${datum.totalCredits?.toLocaleString() || 0} 积分`,
        };
      },
    },
    xAxis: {
      title: {
        text: '日期',
      },
      label: {
        autoRotate: true,
        autoHide: false, // 禁用自动隐藏
        style: {
          fontSize: 12,
        },
        // 使用 filter 函数来控制标签显示间隔
        filter: (_text: string, _item: any, index: number) => {
          // 每间隔1天显示一个标签（根据数据长度动态调整）
          const interval = Math.max(1, Math.ceil(data.length / 8));
          return index % interval === 0;
        },
      },
      tickCount: data.length, // 设置为数据长度，确保所有刻度都存在
      nice: false, // 禁用自动调整，使用原始数据
    },
    yAxis: {
      title: {
        text: '积分消耗',
      },
      label: {
        formatter: (value: string) => {
          const num = Number(value);
          if (num >= 1000000) {
            return `${(num / 1000000).toFixed(1)}M`;
          } else if (num >= 1000) {
            return `${(num / 1000).toFixed(1)}K`;
          }
          return value;
        },
      },
    },
    height: 300,
    padding: [20, 20, 50, 60],
  };

  if (loading) {
    return (
      <Card>
        <div className="flex justify-center items-center h-64">
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <Alert
          message="数据加载失败"
          description={error}
          type="error"
          showIcon
          action={
            <button
              onClick={loadData}
              className="text-blue-500 hover:text-blue-700 underline"
            >
              重试
            </button>
          }
        />
      </Card>
    );
  }

  return (
    <Card
      title="最近30天积分消耗统计"
      className="mb-6"
      extra={
        <button
          onClick={loadData}
          className="text-blue-500 hover:text-blue-700 text-sm"
        >
          刷新
        </button>
      }
    >
      <Column {...config} />
    </Card>
  );
};

export default CreditConsumptionChart;
