{"name": "hivechat", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start -p 3000", "initdb": "npx drizzle-kit push --force", "lint": "next lint"}, "dependencies": {"@agentic/tavily": "^7.6.3", "@amplitude/analytics-browser": "^2.18.2", "@ant-design/icons": "^5.5.1", "@ant-design/nextjs-registry": "^1.0.1", "@anthropic-ai/sdk": "^0.39.0", "@auth/drizzle-adapter": "^1.7.4", "@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@google/generative-ai": "^0.24.0", "@icon-park/react": "^1.4.2", "@microsoft/fetch-event-source": "^2.0.1", "@modelcontextprotocol/sdk": "^1.12.3", "@neondatabase/serverless": "^0.10.4", "antd": "^5.24.6", "bcryptjs": "^2.4.3", "clsx": "^2.1.1", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "drizzle-orm": "^0.41.0", "emoji-mart": "^5.6.0", "github-markdown-css": "^5.7.0", "katex": "^0.16.21", "lodash": "^4.17.21", "next": "^14.2.25", "next-auth": "^5.0.0-beta.25", "next-intl": "^3.26.3", "openai": "^5.5.1", "postgres": "^3.4.5", "react": "^18", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18", "react-markdown": "^8.0.7", "react-syntax-highlighter": "^15.6.1", "recharts": "^3.1.0", "rehype-highlight": "^6.0.0", "rehype-katex": "^6.0.3", "rehype-raw": "^7.0.0", "remark-breaks": "^3.0.3", "remark-gfm": "^3.0.1", "remark-math": "^5.1.1", "sharp": "^0.33.5", "sortablejs": "^1.15.6", "tsx": "^4.19.2", "unified": "^11.0.5", "uuid": "^11.1.0", "zod": "^3.24.1", "zustand": "^5.0.0-rc.2"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "@types/bcryptjs": "^2.4.6", "@types/lodash": "^4.17.13", "@types/node": "^20", "@types/pg": "^8.11.11", "@types/react": "^18", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^18", "@types/react-syntax-highlighter": "^15.5.13", "@types/sortablejs": "^1.15.8", "drizzle-kit": "^0.30.6", "eslint": "^8", "eslint-config-next": "14.2.14", "postcss": "^8", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.1", "typescript": "^5"}}